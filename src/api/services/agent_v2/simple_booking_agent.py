"""
Simple Booking Agent - Clean booking agent for ChatService
"""

import logging
from typing import List, Dict
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.checkpoint.mongodb import MongoDBSaver
from models.user import UserTenantDB
from core.database import get_db_from_tenant_id
from api.services.booking_service import BookingService
from utils.production_memory_manager import get_production_memory_manager
import os
from dotenv import load_dotenv

load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Note: Using shared MongoDB memory instead of separate sessions


class SimpleBookingAgent:
    """
    Simple Booking Agent - Clean booking workflow
    """
    
    def __init__(self, current_user : UserTenantDB=None):
        """Initialize the booking agent"""
        self.current_user = current_user

        # LLM for context understanding
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )

        # Access to search tools through current user
        self.vector_store_manager = current_user.vector_store_manager if current_user else None

        # Initialize booking service for database operations
        if current_user and current_user.tenant_id:
            self.booking_service = BookingService(current_user.tenant_id)
        else:
            self.booking_service = None

        # Initialize production memory system for personalization
        if current_user and current_user.tenant_id:
            self.production_memory = get_production_memory_manager(current_user.tenant_id, "gemini")
        else:
            self.production_memory = None

        # Use the same MongoDB memory as main agent
        if current_user and current_user.tenant_id:
            tenant_db = get_db_from_tenant_id(current_user.tenant_id)
            mongo_client = tenant_db.client
            self.memory = MongoDBSaver(
                client=mongo_client,
                db_name=tenant_db.name,
                collection_name=f"conversation_checkpoints_{current_user.tenant_id}"
            )
            logger.info(f"✅ Booking agent using shared MongoDB memory for tenant: {current_user.tenant_id}")
        else:
            self.memory = None
            logger.warning("⚠️ No memory available for booking agent")

        logger.info("✅ Simple Booking Agent initialized")

    def handle_booking_request(self, user_message: str, thread_id: str) -> str:
        """Handle booking request"""
        try:
            logger.info(f"📅 Booking request: {user_message}")

            # Get conversation context from shared memory
            conversation_context = self._get_conversation_context(thread_id)
            logger.info(f"🔍 Booking agent conversation context available")

            # Check if user already has a selected product
            selected_product = None
            search_results = ""

            if self.production_memory:
                # Production memory doesn't have get_selected_product, use search instead
                try:
                    memories = self.production_memory.search_memories(
                        user_id=str(self.current_user.user.id),
                        query="selected product course",
                        limit=1
                    )
                    if memories and len(memories) > 0:
                        # Extract product info from memory content
                        memory_content = memories[0].content
                        if "selected:" in memory_content.lower():
                            selected_product = {"name": "Previously selected course", "code": "PREV"}
                except Exception as e:
                    logger.warning(f"Could not get selected product from production memory: {e}")
                    selected_product = None

            # Only search for products if no product is selected yet
            if not selected_product and self.vector_store_manager:
                try:
                    # Get the raw search documents first
                    retriever = self.vector_store_manager.get_product_retriever()
                    if retriever:
                        search_docs = retriever.invoke(user_message)

                        # Check if user is selecting from the search results
                        self._check_for_course_selection(user_message, search_docs, thread_id)

                        # Now get the formatted search results
                        search_results = self.vector_store_manager.search_products(user_message)
                        logger.info(f"🔍 Found course search results for booking request")
                    else:
                        search_results = "No courses available at the moment."

                except Exception as e:
                    logger.warning(f"⚠️ Could not search for courses: {e}")
            elif selected_product:
                logger.info(f"✅ Using previously selected product: {selected_product['name']} ({selected_product['code']})")
                search_results = f"Selected Course: {selected_product['name']} (Code: {selected_product['code']})"

            # Get personalized context using production memory
            personalized_context = "No personalization available."
            if self.production_memory:
                try:
                    user_id = str(self.current_user.user.id)
                    personalized_context = self.production_memory.get_user_context(user_id, user_message)
                    logger.info(f"✅ Got personalized context from production memory")
                except Exception as e:
                    logger.warning(f"⚠️ Could not get context from production memory: {e}")



            # Use LLM to understand the booking request with search context
            if selected_product:
                system_prompt = """You are a helpful booking assistant for an educational service center in Nepal.

🌐 LANGUAGE SUPPORT:
- You can understand and respond to messages in Nepali, English, or mixed languages
- When users write in Nepali (नेपाली), understand their intent naturally and respond in English or Romanized Nepali
- Be culturally appropriate and use "Namaste" when greeting Nepali speakers
- CRITICAL NEPALI EXPRESSIONS TO UNDERSTAND:
  * "हजुर" / "hajur" = "Yes" / "Okay" / "I agree"
  * "छ" / "chu" = "Yes" / "Okay" / "Alright"
  * "हुन्छ" / "huncha" = "Okay" / "That's fine"
  * "गर्छु" / "garchu" = "I will do" / "I agree"
  * "चाहिन्छ" / "chahicha" = "I want" / "I need"
- Examples: "बुक गर्न चाहन्छु" = "I want to book", "नाम के हो?" = "What's your name?"
- When user says "hajur", "chu", "huncha" etc., treat it as positive confirmation/agreement

The user has already selected a course. Help them complete the booking by:
1. Confirming their selected course details
2. IMPORTANT: Check the Student Profile first - if name, email, and phone are already available, DO NOT ask for them again. Only ask for missing information.
3. Offering available time slots
4. Finalizing the booking

GREETING BEHAVIOR:
🆕 FOR NEW CUSTOMERS (when profile shows "NEW CUSTOMER"):
- Welcome them warmly to the booking process (use "Namaste" for Nepali speakers)
- Briefly explain what information you'll need
- Be encouraging about their course choice

🔄 FOR RETURNING CUSTOMERS (when profile shows customer name):
- Use their name in greeting with appropriate cultural greeting
- Reference their selected course
- Continue the booking process efficiently

Be friendly and guide them through the process step by step.
Use "Namaste" as greeting when appropriate, especially for Nepali speakers.

CRITICAL RULE: NEVER ask for information that is already provided in the Student Profile. If the profile shows the user's name, email, and phone number, use that information directly and proceed to scheduling.

Student Profile:
{personalized_context}

Selected Course:
{search_context}

Conversation History Context:
{conversation_context}

Focus on completing the booking for their selected course. If all user information is available in the profile, proceed directly to scheduling."""
            else:
                system_prompt = """You are a helpful booking assistant for an educational service center in Nepal.

🌐 LANGUAGE SUPPORT:
- You can understand and respond to messages in Nepali, English, or mixed languages
- When users write in Nepali (नेपाली), understand their intent naturally and respond in English or Romanized Nepali
- Be culturally appropriate and use "Namaste" when greeting Nepali speakers
- CRITICAL NEPALI EXPRESSIONS TO UNDERSTAND:
  * "हजुर" / "hajur" = "Yes" / "Okay" / "I agree"
  * "छ" / "chu" = "Yes" / "Okay" / "Alright"
  * "हुन्छ" / "huncha" = "Okay" / "That's fine"
  * "गर्छु" / "garchu" = "I will do" / "I agree"
  * "चाहिन्छ" / "chahicha" = "I want" / "I need"
- Examples: "कुन कोर्स चाहिन्छ?" = "Which course do you need?", "समय कहिले?" = "What time?"
- When user says "hajur", "chu", "huncha" etc., treat it as positive confirmation/agreement

Help users book courses by:
1. Understanding what course they want to book (use search results if available)
2. Using the student profile to provide personalized recommendations
3. Once they select a course, remember their choice and proceed with booking
4. IMPORTANT: Check the Student Profile first - if name, email, and phone are already available, DO NOT ask for them again. Only ask for missing information.
5. Offering available time slots
6. Confirming the booking and saving it to database

GREETING BEHAVIOR:
🆕 FOR NEW CUSTOMERS (when profile shows "NEW CUSTOMER"):
- Welcome them warmly to the booking process (use "Namaste" for Nepali speakers)
- Ask what course they'd like to book
- Be encouraging and helpful

🔄 FOR RETURNING CUSTOMERS (when profile shows customer name):
- Use their name in greeting with appropriate cultural greeting
- Reference their previous interests if available
- Help them select from available courses

Be friendly and guide them through the process step by step.
Use "Namaste" as greeting when appropriate, especially for Nepali speakers.

CRITICAL RULE: NEVER ask for information that is already provided in the Student Profile. If the profile shows the user's name, email, and phone number, use that information directly.

Student Profile:
{personalized_context}

Available Courses:
{search_context}

Conversation History Context:
{conversation_context}

Respond helpfully to their request with personalized recommendations based on their profile."""

            search_context = f"Available courses from search:\n{search_results}\n" if search_results else "No specific course search results available."

            response = self.llm.invoke([
                SystemMessage(content=system_prompt.format(
                    personalized_context=personalized_context,
                    search_context=search_context,
                    conversation_context=conversation_context
                )),
                HumanMessage(content=f"User message: {user_message}")
            ])

            # Product selection is handled in _check_for_course_selection above

            result = response.content

            # Track incomplete process if booking was started but not completed
            self._track_booking_progress(user_message, result, thread_id)

            logger.info(f"✅ Booking response generated")
            return result
            
        except Exception as e:
            error_msg = f"Error handling booking: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble processing your booking request. Please try again or contact our support team."

    def _get_conversation_context(self, thread_id: str) -> str:
        """Get conversation context from shared MongoDB memory"""
        if not self.memory:
            return "No conversation history available."

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if checkpoint:
                # Handle both dict and object checkpoint formats
                if isinstance(checkpoint, dict):
                    messages = checkpoint.get("channel_values", {}).get("messages", [])
                elif hasattr(checkpoint, 'channel_values'):
                    messages = checkpoint.channel_values.get("messages", [])
                else:
                    messages = []

                # Return recent conversation context for the agent to use
                context_info = []
                for msg in messages[-10:]:  # Last 10 messages for context
                    if hasattr(msg, 'content'):
                        context_info.append(f"Message: {msg.content}")

                return "\n".join(context_info) if context_info else "No conversation history found."
            else:
                return "No conversation history found."

        except Exception as e:
            logger.warning(f"Could not retrieve conversation context: {e}")
            return "Error retrieving conversation history."

    def _get_conversation_messages(self, thread_id: str) -> List[Dict[str, str]]:
        """Get conversation messages in format suitable for memory system"""
        if not self.memory:
            return []

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if checkpoint:
                # Handle both dict and object checkpoint formats
                if isinstance(checkpoint, dict):
                    messages = checkpoint.get("channel_values", {}).get("messages", [])
                elif hasattr(checkpoint, 'channel_values'):
                    messages = checkpoint.channel_values.get("messages", [])
                else:
                    messages = []

                # Convert to memory system format
                conversation_messages = []
                for msg in messages[-20:]:  # Last 20 messages for better context
                    if hasattr(msg, 'content') and msg.content:
                        # Determine role based on message type
                        if hasattr(msg, 'type'):
                            if msg.type == 'human':
                                role = 'user'
                            elif msg.type == 'ai':
                                role = 'assistant'
                            else:
                                role = 'user'  # Default to user
                        else:
                            role = 'user'  # Default to user

                        conversation_messages.append({
                            "role": role,
                            "content": msg.content
                        })

                return conversation_messages
            else:
                return []

        except Exception as e:
            logger.warning(f"Could not retrieve conversation messages: {e}")
            return []

    def _check_for_course_selection(self, user_message: str, search_results: str, _thread_id: str) -> None:
        """Use LLM to intelligently detect course selection from context"""
        try:
            # Use LLM to understand if user is selecting a course
            selection_prompt = f"""
You are analyzing a conversation where a user is selecting a course from available options.
The user may communicate in Nepali, English, or mixed languages.

User message: "{user_message}"

Available courses from previous search:
{search_results}

LANGUAGE UNDERSTANDING:
- Understand Nepali expressions like: "हो" (yes), "चाहिन्छ" (want), "गर्छु" (will do), "लिन्छु" (will take)
- Understand English expressions like: "yes", "I want", "book this", "take this"
- Understand mixed language: "हो SEE Bridge course चाहिन्छ"

If the user is clearly selecting or confirming a specific course, extract the course name and code.
Respond in this exact format:
SELECTED: [Course Name] | [Course Code]

If no clear selection is made, respond:
NO_SELECTION

Examples:
- "Yes SEE Bridge Course" → SELECTED: SEE Bridge Course | SEE-BRIDGE
- "हो SEE Bridge चाहिन्छ" → SELECTED: SEE Bridge Course | SEE-BRIDGE
- "I want the IELTS course" → SELECTED: IELTS Preparation | IELTS-PREP
- "IELTS लिन्छु" → SELECTED: IELTS Preparation | IELTS-PREP
- "Book the first one" → SELECTED: [First course from list] | [Its code]
- "पहिलो वाला लिन्छु" → SELECTED: [First course from list] | [Its code]
- "What other options do you have?" → NO_SELECTION
- "अरु के छ?" → NO_SELECTION
"""

            response = self.llm.invoke([
                SystemMessage(content="You are a course selection detector. Extract course selections from user messages."),
                HumanMessage(content=selection_prompt)
            ])

            response_text = response.content.strip()

            if response_text.startswith("SELECTED:"):
                # Parse the selection
                selection_part = response_text.replace("SELECTED:", "").strip()
                if "|" in selection_part:
                    course_name, course_code = selection_part.split("|", 1)
                    course_name = course_name.strip()
                    course_code = course_code.strip()

                    # Save the selected product to production memory
                    if self.production_memory:
                        try:
                            user_id = str(self.current_user.user.id)
                            from utils.structured_memory import MemoryType, ImportanceLevel
                            self.production_memory.save_structured_memory(
                                user_id=user_id,
                                content=f"User selected course: {course_name} (Code: {course_code})",
                                memory_type=MemoryType.COURSE_INTEREST,
                                importance=ImportanceLevel.HIGH
                            )
                            logger.info(f"🎯 User selected course: {course_name} ({course_code})")
                        except Exception as e:
                            logger.warning(f"Could not save selection to production memory: {e}")

        except Exception as e:
            logger.warning(f"⚠️ Could not check course selection: {e}")

    def _track_booking_progress(self, user_message: str, agent_response: str, thread_id: str) -> None:
        """Track booking progress and identify incomplete processes"""
        try:
            if not self.production_memory:
                return

            user_id = str(self.current_user.user.id)

            # Check if this looks like an incomplete booking process
            user_msg_lower = user_message.lower()
            response_lower = agent_response.lower()

            # Indicators that a booking process was started
            booking_started_indicators = [
                "want to book", "interested in", "book this course",
                "enroll in", "sign up for", "register for"
            ]

            # Indicators that booking is incomplete
            incomplete_indicators = [
                "need more information", "let me know", "would you like to",
                "please provide", "what time", "when would you prefer",
                "need to confirm", "available slots", "contact details"
            ]

            # Check if booking was started
            booking_started = any(indicator in user_msg_lower for indicator in booking_started_indicators)

            # Check if booking is incomplete (agent is asking for more info)
            booking_incomplete = any(indicator in response_lower for indicator in incomplete_indicators)

            if booking_started and booking_incomplete:
                # Extract course name from the conversation
                course_name = self._extract_course_from_conversation(user_message, agent_response)

                # Track the incomplete process
                process_details = f"Booking process started for {course_name}" if course_name else "Course booking process started"
                next_steps = "Collect user details and confirm booking"

                # Use the track_incomplete_process tool if available
                memory_tools = self.production_memory.create_memory_tools(self.current_user)
                track_tool = None
                for tool in memory_tools:
                    if hasattr(tool, 'name') and tool.name == 'track_incomplete_process':
                        track_tool = tool
                        break

                if track_tool:
                    try:
                        track_tool.invoke({
                            "process_type": "booking",
                            "process_details": process_details,
                            "next_steps": next_steps
                        })
                        logger.info(f"📝 Tracked incomplete booking process: {process_details}")
                    except Exception as e:
                        logger.warning(f"Could not track incomplete process: {e}")

        except Exception as e:
            logger.warning(f"Error tracking booking progress: {e}")

    def _extract_course_from_conversation(self, user_message: str, agent_response: str) -> str:
        """Extract course name from conversation context"""
        try:
            combined_text = f"{user_message} {agent_response}".lower()

            # Common course patterns
            course_patterns = [
                ("see bridge", "SEE Bridge Course"),
                ("ielts", "IELTS Preparation"),
                ("german", "German Language Course"),
                ("korean", "Korean Language Course"),
                ("japanese", "Japanese Language Course"),
                ("bbs", "BBS Program"),
                ("bba", "BBA Program"),
                ("csit", "CSIT Program"),
                ("english", "English Language Course")
            ]

            for pattern, full_name in course_patterns:
                if pattern in combined_text:
                    return full_name

            # Try to extract course code pattern (e.g., SEE-BRIDGE)
            import re
            course_code_match = re.search(r'([A-Z]+-[A-Z]+)', combined_text.upper())
            if course_code_match:
                return course_code_match.group(1)

        except Exception as e:
            logger.warning(f"Error extracting course from conversation: {e}")

        return "Unknown Course"

    def get_pending_booking_reminder(self, _thread_id: str) -> str:
        """Get reminder message for pending booking"""
        # Since we're using shared memory, no separate booking reminders needed
        return ""
